"use client"

import type React from "react"

import { createClient } from "@supabase/supabase-js"
import { useState, createContext, useContext } from "react"

// Get environment variables with type checking
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables")
}

// Create the Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
})

// Create a mock client for development
const createMockClient = () => {
  if (process.env.NODE_ENV === "production") {
    return supabase
  }

  console.warn("Using mock Supabase client - authentication features will be limited")
  return {
    auth: {
      getSession: async () => ({ data: { session: null }, error: null }),
      signInWithOAuth: async () => ({ data: null, error: null }),
      signInWithPassword: async () => ({ data: null, error: null }),
      signUp: async () => ({ data: null, error: null }),
      signOut: async () => ({ error: null }),
      onAuthStateChange: () => ({
        data: { subscription: { unsubscribe: () => {} } },
        subscription: { unsubscribe: () => {} },
      }),
    },
    from: () => ({
      select: () => ({
        single: async () => null,
        eq: () => ({ single: async () => null }),
        order: () => ({ data: [], error: null }),
      }),
      upsert: async () => ({ data: null, error: null }),
      delete: () => ({ eq: () => ({ data: null, error: null }) }),
    }),
    channel: (name: string) => ({
      on: () => ({ subscribe: (callback: any) => callback("CHANNEL_ERROR") }),
      subscribe: (callback: any) => callback("CHANNEL_ERROR"),
      unsubscribe: () => {},
      presenceState: () => ({}),
      track: async () => ({}),
      send: async () => false,
    }),
  }
}

// Export the appropriate client based on environment
export const client = process.env.NODE_ENV === "development" ? createMockClient() : supabase

// Create a context for the Supabase client
const SupabaseContext = createContext<typeof supabase | null>(null)

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const [supabaseClient] = useState(() => supabase)

  return (
    <SupabaseContext.Provider value={supabaseClient}>
      {children}
    </SupabaseContext.Provider>
  )
}

export const useSupabase = () => {
  const context = useContext(SupabaseContext)
  if (!context) {
    throw new Error("useSupabase must be used within a SupabaseProvider")
  }
  return context
}

// Helper function to get the current user
export async function getCurrentUser() {
  try {
    const { data: { session } } = await supabase.auth.getSession()
    return session?.user
  } catch (error) {
    console.error("Error getting current user:", error)
    return null
  }
}

// Helper function to sign out
export async function signOut() {
  try {
    return await supabase.auth.signOut()
  } catch (error) {
    console.error("Error signing out:", error)
    return { error: null }
  }
}

// Standard Supabase OAuth implementation
export async function signInWithOAuth(provider: string, options: any = {}) {
  try {
    // Use Supabase's standard OAuth implementation
    return await supabase.auth.signInWithOAuth({
      provider: provider as any,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        scopes: getProviderScopes(provider),
        ...options,
      },
    })
  } catch (error) {
    console.error(`Error signing in with ${provider}:`, error)
    return { data: null, error }
  }
}

// Helper function to get appropriate scopes for each provider
function getProviderScopes(provider: string): string {
  switch (provider) {
    case "spotify":
      return "user-read-email user-read-private user-top-read playlist-read-private"
    case "google":
      return "https://www.googleapis.com/auth/youtube.readonly"
    case "soundcloud":
      return "non-expiring"
    default:
      return ""
  }
}

// Helper function for email/password sign in
export async function signInWithEmail(email: string, password: string) {
  try {
    return await supabase.auth.signInWithPassword({
      email,
      password,
    })
  } catch (error) {
    console.error("Error signing in with email:", error)
    return { data: { user: null, session: null }, error }
  }
}

// Helper function for email/password sign up
export async function signUpWithEmail(email: string, password: string) {
  try {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    })
  } catch (error) {
    console.error("Error signing up with email:", error)
    return { data: { user: null, session: null }, error }
  }
}

// Helper function to save OAuth tokens to Supabase
export async function saveOAuthTokens(provider: string, tokens: any) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new Error("No authenticated user found")
    }

    // Store the tokens in the user's metadata or a separate table
    const { data, error } = await supabase.from("user_oauth_tokens").upsert({
      user_id: user.id,
      provider,
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      expires_at: new Date(Date.now() + tokens.expires_in * 1000).toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error("Error saving OAuth tokens:", error)
    return null
  }
}
