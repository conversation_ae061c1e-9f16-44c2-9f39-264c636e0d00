"use client"

import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"
import type { Track } from "@/contexts/music-player-context"

export function useSupabasePlayer() {
  const [tracks, setTracks] = useState<Track[]>([])
  const [playlists, setPlaylists] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch tracks
  const fetchTracks = async () => {
    try {
      setLoading(true)

      // In development mode with DEV_AUTO_LOGIN enabled, use mock data
      if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true') {
        console.log('Development mode: Using mock track data')

        // Mock track data for development
        const mockTracks = [
          {
            id: 'mock-track-1',
            title: 'Development Track 1',
            artist: 'Dev Artist',
            artwork_url: 'https://placehold.co/400',
            audio_url: 'https://example.com/track1.mp3',
            duration: 180,
            created_at: new Date().toISOString(),
            spotify_id: 'mock-spotify-1',
            isrc: 'USABC1234567'
          },
          {
            id: 'mock-track-2',
            title: 'Development Track 2',
            artist: 'Dev Artist 2',
            artwork_url: 'https://placehold.co/400',
            audio_url: 'https://example.com/track2.mp3',
            duration: 240,
            created_at: new Date().toISOString(),
            spotify_id: 'mock-spotify-2',
            isrc: 'USABC7654321'
          }
        ]

        setTracks(mockTracks)
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from("tracks")
        .select("*")
        .order("created_at", { ascending: false })

      if (error) throw error

      const formattedTracks = data.map((track) => ({
        id: track.id,
        title: track.title,
        artist: track.artist,
        artwork_url: track.artwork_url,
        audio_url: track.audio_url,
        duration: track.duration,
        created_at: track.created_at,
        spotify_id: track.spotify_id,
        isrc: track.isrc,
      }))

      setTracks(formattedTracks)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Fetch playlists
  const fetchPlaylists = async () => {
    try {
      setLoading(true)

      // In development mode with DEV_AUTO_LOGIN enabled, use mock data
      if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true') {
        console.log('Development mode: Using mock playlist data')

        // Mock playlist data for development
        const mockPlaylists = [
          {
            id: 'mock-playlist-1',
            name: 'Development Playlist 1',
            description: 'Mock playlist for development',
            created_at: new Date().toISOString(),
            is_public: true,
            playlist_tracks: [
              {
                track_id: 'mock-track-1',
                position: 0,
                tracks: {
                  id: 'mock-track-1',
                  title: 'Development Track 1',
                  artist: 'Dev Artist',
                  artwork_url: 'https://placehold.co/400',
                  audio_url: 'https://example.com/track1.mp3',
                  duration: 180,
                  created_at: new Date().toISOString(),
                  spotify_id: 'mock-spotify-1',
                  isrc: 'USABC1234567'
                }
              },
              {
                track_id: 'mock-track-2',
                position: 1,
                tracks: {
                  id: 'mock-track-2',
                  title: 'Development Track 2',
                  artist: 'Dev Artist 2',
                  artwork_url: 'https://placehold.co/400',
                  audio_url: 'https://example.com/track2.mp3',
                  duration: 240,
                  created_at: new Date().toISOString(),
                  spotify_id: 'mock-spotify-2',
                  isrc: 'USABC7654321'
                }
              }
            ]
          },
          {
            id: 'mock-playlist-2',
            name: 'Development Playlist 2',
            description: 'Another mock playlist for development',
            created_at: new Date().toISOString(),
            is_public: true,
            playlist_tracks: []
          }
        ]

        setPlaylists(mockPlaylists)
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from("playlists")
        .select(`
          *,
          playlist_tracks (
            track_id,
            position,
            tracks (*)
          )
        `)
        .order("created_at", { ascending: false })

      if (error) throw error

      setPlaylists(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Add track to playlist
  const addTrackToPlaylist = async (playlistId: string, trackId: string, position: number) => {
    try {
      const { error } = await supabase.from("playlist_tracks").insert({
        playlist_id: playlistId,
        track_id: trackId,
        position,
      })

      if (error) throw error

      // Refresh playlists
      await fetchPlaylists()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  // Remove track from playlist
  const removeTrackFromPlaylist = async (playlistId: string, trackId: string) => {
    try {
      const { error } = await supabase
        .from("playlist_tracks")
        .delete()
        .match({ playlist_id: playlistId, track_id: trackId })

      if (error) throw error

      // Refresh playlists
      await fetchPlaylists()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  // Create new playlist
  const createPlaylist = async (name: string, description?: string, isPublic: boolean = false) => {
    try {
      const { data, error } = await supabase
        .from("playlists")
        .insert({
          name,
          description,
          is_public: isPublic,
        })
        .select()
        .single()

      if (error) throw error

      // Refresh playlists
      await fetchPlaylists()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      return null
    }
  }

  // Upload track
  const uploadTrack = async (track: Omit<Track, "id" | "created_at">) => {
    try {
      const { data, error } = await supabase.from("tracks").insert(track).select().single()

      if (error) throw error

      // Refresh tracks
      await fetchTracks()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      return null
    }
  }

  // Delete track
  const deleteTrack = async (trackId: string) => {
    try {
      const { error } = await supabase.from("tracks").delete().eq("id", trackId)

      if (error) throw error

      // Refresh tracks
      await fetchTracks()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  // Initialize data
  useEffect(() => {
    fetchTracks()
    fetchPlaylists()
  }, [])

  return {
    tracks,
    playlists,
    loading,
    error,
    fetchTracks,
    fetchPlaylists,
    addTrackToPlaylist,
    removeTrackFromPlaylist,
    createPlaylist,
    uploadTrack,
    deleteTrack,
  }
}
