"use client"

import { useState } from "react"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export function MinimalUrlForm() {
  const [url, setUrl] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!url.trim()) return

    setIsSubmitting(true)

    try {
      // Insert the URL into the submissions table
      const { error } = await supabase
        .from("submissions")
        .insert({
          url: url,
          media_url: url,
          artist_name: "From URL",
          song_title: "New Submission",
          submission_type: "Free",
          status: "pending",
          created_at: new Date().toISOString()
        })

      if (error) throw error

      setUrl("")
      alert("URL submitted successfully")
    } catch (error) {
      console.error("Error submitting URL:", error)
      alert("Failed to submit URL")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="flex space-x-2">
      <Input
        value={url}
        onChange={(e) => setUrl(e.target.value)}
        placeholder="Enter URL (YouTube, SoundCloud, etc.)"
        className="flex-1 bg-black/40 border-purple-500/30"
      />
      <Button
        type="submit"
        disabled={isSubmitting}
        className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white"
      >
        {isSubmitting ? "Submitting..." : "Submit"}
      </Button>
    </form>
  )
}
