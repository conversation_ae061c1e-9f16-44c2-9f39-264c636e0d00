"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight, Settings, LayoutDashboard, ImageIcon, Users, Library, FileMusic } from "lucide-react"

// Define the admin pages to cycle through
const adminPages = [
  { path: "/admin", label: "Dashboard", icon: <LayoutDashboard className="h-5 w-5" /> },
  { path: "/admin/settings", label: "Settings", icon: <Settings className="h-5 w-5" /> },
  { path: "/admin/images", label: "Images", icon: <ImageIcon className="h-5 w-5" /> },
  { path: "/admin/musiclibrary", label: "Music Library", icon: <Library className="h-5 w-5" /> },
  { path: "/admin/m3u8-playlists", label: "M3U8 Playlists", icon: <FileMusic className="h-5 w-5" /> },
  { path: "/admin/users", label: "Users", icon: <Users className="h-5 w-5" /> },
]

export const AdminsBigDock = () => {
  const [isVisible, setIsVisible] = useState(true)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [direction, setDirection] = useState(0) // -1 for left, 1 for right
  const router = useRouter()
  const pathname = usePathname()

  // Find current page index
  const getCurrentPageIndex = () => {
    // Check if we're on an admin page
    if (!pathname?.startsWith("/admin")) return 0

    // Find exact match first
    const exactIndex = adminPages.findIndex((page) => page.path === pathname)
    if (exactIndex >= 0) return exactIndex

    // If no exact match but we're on an admin page, return the dashboard
    return 0
  }

  useEffect(() => {
    const handleScroll = () => {
      // Hide when scrolled more than 100px
      setIsVisible(window.scrollY <= 100)
    }

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll)

    // Initial check
    handleScroll()

    // Clean up
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Navigate to the next page in the cycle
  const navigateNext = () => {
    if (isTransitioning) return

    setIsTransitioning(true)
    setDirection(1)

    const currentIndex = getCurrentPageIndex()
    const nextIndex = (currentIndex + 1) % adminPages.length

    // Use a slightly longer transition for carousel effect
    setTimeout(() => {
      router.push(adminPages[nextIndex].path)
      setTimeout(() => setIsTransitioning(false), 500)
    }, 300)
  }

  // Navigate to the previous page in the cycle
  const navigatePrev = () => {
    if (isTransitioning) return

    setIsTransitioning(true)
    setDirection(-1)

    const currentIndex = getCurrentPageIndex()
    const prevIndex = (currentIndex - 1 + adminPages.length) % adminPages.length

    // Use a slightly longer transition for carousel effect
    setTimeout(() => {
      router.push(adminPages[prevIndex].path)
      setTimeout(() => setIsTransitioning(false), 500)
    }, 300)
  }

  return (
    <>
      {/* Page transition overlay */}
      <AnimatePresence>
        {isTransitioning && (
          <motion.div
            key="page-transition"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 pointer-events-none overflow-hidden"
          >
            <motion.div
              initial={{
                opacity: 0,
                x: direction > 0 ? "100%" : "-100%",
              }}
              animate={{
                opacity: 1,
                x: "0%",
              }}
              exit={{
                opacity: 0,
                x: direction > 0 ? "-100%" : "100%",
              }}
              transition={{
                duration: 0.5,
                ease: "easeInOut",
              }}
              className="absolute inset-0 bg-black/80"
            >
              {/* Carousel slide effect */}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.8, opacity: 0 }}
                  transition={{ duration: 0.4 }}
                  className="text-[#9333ea] text-4xl font-bold"
                >
                  {adminPages[getCurrentPageIndex()].label}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main AdminsBigDock */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            className="fixed top-2.5 left-1/2 transform -translate-x-1/2 z-50"
            initial={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex h-16 items-center rounded-full bg-black/80 backdrop-blur-md px-6 py-2 border-2 border-purple-500/50 shadow-[0_0_15px_rgba(147,51,234,0.5)] transition-all duration-300 gap-4">
              {/* Previous button */}
              <button
                className="w-10 h-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-purple-500 transition-all duration-300 shadow-lg"
                style={{
                  background:
                    "radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                  boxShadow:
                    "inset 0 0 15px rgba(147, 51, 234, 0.5), 0 0 10px rgba(147, 51, 234, 0.7), 0 0 20px rgba(147, 51, 234, 0.3)",
                  backdropFilter: "blur(4px)",
                  transform: "translateZ(0)",
                }}
                onClick={navigatePrev}
              >
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                    transform: "translateZ(5px)",
                  }}
                ></div>
                <ChevronLeft className="text-white" />
                <span className="sr-only">Previous</span>
              </button>

              {/* Shortcut buttons */}
              {adminPages.map((page, index) => (
                <Link href={page.path} key={page.path}>
                  <button
                    className={`w-10 h-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 transition-all duration-300 shadow-lg ${
                      pathname === page.path
                        ? "border-purple-500 shadow-[0_0_15px_rgba(147,51,234,0.7)]"
                        : "border-purple-300/50"
                    }`}
                    style={{
                      background:
                        pathname === page.path
                          ? "radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.3), rgba(0, 0, 0, 0.8) 60%, transparent 70%)"
                          : "radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.1), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                      boxShadow:
                        pathname === page.path
                          ? "inset 0 0 15px rgba(147, 51, 234, 0.5), 0 0 10px rgba(147, 51, 234, 0.7), 0 0 20px rgba(147, 51, 234, 0.3)"
                          : "inset 0 0 10px rgba(147, 51, 234, 0.3)",
                      backdropFilter: "blur(4px)",
                      transform: "translateZ(0)",
                    }}
                  >
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                        transform: "translateZ(5px)",
                      }}
                    ></div>
                    {page.icon}
                    <span className="sr-only">{page.label}</span>
                  </button>
                </Link>
              ))}

              {/* Next button */}
              <button
                className="w-10 h-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-purple-500 transition-all duration-300 shadow-lg"
                style={{
                  background:
                    "radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                  boxShadow:
                    "inset 0 0 15px rgba(147, 51, 234, 0.5), 0 0 10px rgba(147, 51, 234, 0.7), 0 0 20px rgba(147, 51, 234, 0.3)",
                  backdropFilter: "blur(4px)",
                  transform: "translateZ(0)",
                }}
                onClick={navigateNext}
              >
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                    transform: "translateZ(5px)",
                  }}
                ></div>
                <ChevronRight className="text-white" />
                <span className="sr-only">Next</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating corner docks - visible when main dock is not visible */}
      {!isVisible && (
        <>
          {/* Left corner dock - Previous button */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed top-10 left-4 z-50"
          >
            <button
              className="w-10 h-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-purple-500 transition-all duration-300 shadow-lg"
              style={{
                background:
                  "radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                boxShadow:
                  "inset 0 0 15px rgba(147, 51, 234, 0.5), 0 0 10px rgba(147, 51, 234, 0.7), 0 0 20px rgba(147, 51, 234, 0.3)",
                backdropFilter: "blur(4px)",
                transform: "translateZ(0)",
              }}
              onClick={navigatePrev}
            >
              <div
                className="absolute inset-0 rounded-full"
                style={{
                  background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                  transform: "translateZ(5px)",
                }}
              ></div>
              <ChevronLeft className="text-white" />
              <span className="sr-only">Previous</span>
            </button>
          </motion.div>

          {/* Right corner dock - Forward button */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed top-10 right-4 z-50"
          >
            <button
              className="w-10 h-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-purple-500 transition-all duration-300 shadow-lg"
              style={{
                background:
                  "radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                boxShadow:
                  "inset 0 0 15px rgba(147, 51, 234, 0.5), 0 0 10px rgba(147, 51, 234, 0.7), 0 0 20px rgba(147, 51, 234, 0.3)",
                backdropFilter: "blur(4px)",
                transform: "translateZ(0)",
              }}
              onClick={navigateNext}
            >
              <div
                className="absolute inset-0 rounded-full"
                style={{
                  background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                  transform: "translateZ(5px)",
                }}
              ></div>
              <ChevronRight className="text-white" />
              <span className="sr-only">Next</span>
            </button>
          </motion.div>
        </>
      )}
    </>
  )
}
