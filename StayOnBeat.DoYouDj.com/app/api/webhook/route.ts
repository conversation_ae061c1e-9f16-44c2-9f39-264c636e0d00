import { NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import { getDatabaseClient } from '@/lib/database';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Define webhook payload schema
const webhookSchema = z.object({
  event: z.string(),
  data: z.record(z.unknown()),
  timestamp: z.string().datetime(),
});

export async function POST(request: Request) {
  try {
    // Get the webhook signature from headers
    const signature = request.headers.get('x-webhook-signature');

    // Verify webhook signature (implement your verification logic here)
    if (!signature) {
      return NextResponse.json(
        { error: 'Missing webhook signature' },
        { status: 401 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = webhookSchema.safeParse(body);

    if (!validatedData.success) {
      return NextResponse.json(
        { error: 'Invalid webhook payload', details: validatedData.error },
        { status: 400 }
      );
    }

    // Store webhook event in Supabase
    const { error } = await supabase
      .from('webhook_events')
      .insert({
        event_type: validatedData.data.event,
        payload: validatedData.data.data,
        received_at: validatedData.data.timestamp,
        signature: signature,
      });

    if (error) {
      console.error('Error storing webhook event:', error);
      return NextResponse.json(
        { error: 'Failed to process webhook' },
        { status: 500 }
      );
    }

    // Process the webhook based on event type
    switch (validatedData.data.event) {
      case 'submission.created':
        // Handle new submission - trigger real-time update
        await handleSubmissionCreated(validatedData.data.data);
        break;
      case 'submission.updated':
        // Handle submission update - trigger real-time update
        await handleSubmissionUpdated(validatedData.data.data);
        break;
      case 'track.processed':
        // Handle track processing completion
        await handleTrackProcessed(validatedData.data.data);
        break;
      case 'user.created':
        // Handle user creation
        break;
      case 'user.updated':
        // Handle user update
        break;
      // Add more event handlers as needed
      default:
        console.log(`Unhandled event type: ${validatedData.data.event}`);
    }

    return NextResponse.json({ status: 'success' });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Webhook handler functions for real-time updates
async function handleSubmissionCreated(data: any) {
  try {
    console.log('🎵 New submission webhook received:', data);

    // Update database with webhook data
    const database = getDatabaseClient();

    // If using Supabase, the real-time subscription will automatically trigger
    // For local database, we could implement a custom broadcast mechanism

    // Log for debugging
    console.log('✅ Submission created webhook processed successfully');
  } catch (error) {
    console.error('❌ Error processing submission created webhook:', error);
  }
}

async function handleSubmissionUpdated(data: any) {
  try {
    console.log('🔄 Submission updated webhook received:', data);

    // Update database with webhook data
    const database = getDatabaseClient();

    // Log for debugging
    console.log('✅ Submission updated webhook processed successfully');
  } catch (error) {
    console.error('❌ Error processing submission updated webhook:', error);
  }
}

async function handleTrackProcessed(data: any) {
  try {
    console.log('🎧 Track processed webhook received:', data);

    // Update submission status to completed
    const database = getDatabaseClient();

    if (data.submissionId) {
      await database
        .from('submissions')
        .update({
          status: 'completed',
          processing_status: 'completed',
          metadata_extracted: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.submissionId);
    }

    console.log('✅ Track processed webhook completed successfully');
  } catch (error) {
    console.error('❌ Error processing track processed webhook:', error);
  }
}