import { NextResponse } from 'next/server'
import { getDatabaseClient } from '@/lib/database'

export async function GET() {
  try {
    console.log('🧪 Testing submissions API endpoint...')
    
    const database = getDatabaseClient()
    console.log('🧪 Database client obtained')
    
    // Test simple query without filters
    const { data: allData, error: allError } = await database
      .from("submissions")
      .select("*")
    
    console.log('🧪 All submissions data:', allData)
    console.log('🧪 All submissions error:', allError)
    
    // Test with order
    const { data: orderedData, error: orderedError } = await database
      .from("submissions")
      .select("*")
      .order("created_at", { ascending: false })
    
    console.log('🧪 Ordered submissions data:', orderedData)
    console.log('🧪 Ordered submissions error:', orderedError)
    
    return NextResponse.json({
      success: true,
      allSubmissions: {
        data: allData,
        error: allError,
        count: allData?.length || 0
      },
      orderedSubmissions: {
        data: orderedData,
        error: orderedError,
        count: orderedData?.length || 0
      }
    })
    
  } catch (error) {
    console.error('🧪 Test submissions API error:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
