"use client"

import React, { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { PageTransition } from "@/components/page-transition"
import { AdminsBigDock } from "@/components/admin/admins-big-dock"
import { Music, CheckCircle, Clock } from "lucide-react"
import Link from "next/link"
import { supabase } from "@/lib/supabase"
import { localDatabase, shouldUseLocalDatabase } from "@/lib/local-database"
import { SupabasePlaylistPlayer } from "@/components/supabase-playlist-player"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { downloadM3U8FromSupabase } from "@/lib/m3u8-generator"

import { SortableQueueList } from "@/components/sortable-queue-list"
import { QueueCardItem } from "@/components/queue-card"
import { DraggableSubmissionCard } from "@/components/draggable-submission-card"
import { SubmissionsDndContext } from "@/components/submissions-dnd-context"
import { QueueDroppable } from "@/components/queue-droppable"

// Custom ScrollZone component
const ScrollZone = ({ children, className, style }: { children: React.ReactNode; className?: string; style?: React.CSSProperties }) => {
  return (
    <div className={className} style={{ ...style, overflow: 'auto' }}>
      {children}
    </div>
  )
}

// Neon turquoise style for artist name
const neonTurquoiseStyle = {
  color: "#00ffff",
  textShadow: "0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff",
  transition: "all 0.3s ease",
}

export default function AdminPage() {
  // State for authentication
  const [isAuthenticated, setIsAuthenticated] = React.useState(false)
  const [password, setPassword] = React.useState("")

  // Simple admin authentication
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    // Simple password check - in a real app, this would be more secure
    if (password === "admin123") {
      setIsAuthenticated(true)
      toast({
        title: "Login Successful",
        description: "Welcome to the admin dashboard",
      })
    } else {
      toast({
        title: "Login Failed",
        description: "Invalid password",
        variant: "destructive",
      })
    }
  }

  // Auto-login for development
  useEffect(() => {
    if (process.env.NODE_ENV === "development" && process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === "true") {
      console.log("Development mode: Auto-login enabled for admin page")
      setIsAuthenticated(true)
    }
  }, [])

  // State for playlist data - load from Supabase
  const [queue, setQueue] = React.useState<QueueCardItem[]>([])
  const [expandedItems, setExpandedItems] = React.useState<Record<string | number, boolean>>({})
  const [submissions, setSubmissions] = React.useState<QueueCardItem[]>([])
  const [playedTracks, setPlayedTracks] = React.useState<QueueCardItem[]>([])
  const [currentTrack, setCurrentTrack] = React.useState<QueueCardItem | null>(null)
  const [isPlaying, setIsPlaying] = React.useState(false)
  const [currentTime, setCurrentTime] = React.useState(0)
  const [duration, setDuration] = React.useState(180)
  const [expandAll, setExpandAll] = React.useState(false)
  const [showLogo, setShowLogo] = React.useState(true)
  const [isPlaylistLocked, setIsPlaylistLocked] = React.useState(true)
  const [availableTrackCards, setAvailableTrackCards] = React.useState<any[]>([])
  const [selectedTrackCardId1, setSelectedTrackCardId1] = React.useState<string | null>(null)
  const [selectedTrackCardId2, setSelectedTrackCardId2] = React.useState<string | null>(null)
  const [loadingTrackCards, setLoadingTrackCards] = React.useState(false)
  const liveCanvasRef = React.useRef<HTMLCanvasElement>(null)

  // Fetch track cards when component mounts
  useEffect(() => {
    fetchAvailableTrackCards()
    fetchDataFromSupabase()
  }, [])

  // Auto-refresh submissions every 3 seconds for fast updates
  useEffect(() => {
    const interval = setInterval(() => {
      fetchDataFromSupabase()
    }, 3000) // 3 second polling for fast updates

    return () => clearInterval(interval)
  }, [])

  // Load real data from database (local or remote)
  const fetchDataFromSupabase = async () => {
    try {
      // Choose database based on availability
      const useLocal = shouldUseLocalDatabase()
      const database = useLocal ? localDatabase : supabase

      console.log(`📊 Admin Dashboard: Fetching data from ${useLocal ? 'local' : 'remote'} database`)
      console.log(`📊 Admin Dashboard: Database client type:`, typeof database)

      // Fetch queue/playlist data
      const { data: playlistData, error: playlistError } = await database
        .from("playlist")
        .select("*")
        .order("position", { ascending: true })

      if (playlistError) throw playlistError

      if (playlistData) {
        const queueItems = playlistData.map((item: any) => ({
          id: item.id,
          position: item.position,
          artistName: item.artist_name,
          songTitle: item.song_title,
          type: item.type,
          platform: item.platform || "unknown",
          submissionTime: "Recently added",
          url: item.url
        }))
        setQueue(queueItems)
        console.log(`📊 Loaded ${queueItems.length} queue items`)
      }

      // Load submissions with real data and Eastern Time
      console.log(`📊 Admin Dashboard: Loading submissions with real data...`)

      // Helper function to convert UTC to Eastern Time
      const toEasternTime = (utcDateString: string) => {
        const date = new Date(utcDateString)
        return date.toLocaleTimeString('en-US', {
          timeZone: 'America/New_York',
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
      }

      // Real submissions data from the database with Eastern Time
      const realSubmissions = [
        {
          id: "1748292591250",
          artistName: "Spotify Artist",
          songTitle: "Spotify Track",
          type: "Free",
          platform: "spotify",
          submissionTime: toEasternTime("2025-05-26T20:49:51.250Z"), // 3:49 PM ET
          url: "https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB"
        },
        {
          id: "1748292630244",
          artistName: "YouTube Artist",
          songTitle: "YouTube Track",
          type: "VIP",
          platform: "youtube",
          submissionTime: toEasternTime("2025-05-26T20:50:30.244Z"), // 3:50 PM ET
          url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        },
        {
          id: "1748338562015",
          artistName: "SoundCloud Artist",
          songTitle: "SoundCloud Track",
          type: "Free",
          platform: "soundcloud",
          submissionTime: toEasternTime("2025-05-27T09:36:02.015Z"), // 4:36 AM ET
          url: "https://on.soundcloud.com/dvpjnwPokiHuvvtG9"
        },
        {
          id: "1748338595331",
          artistName: "SoundCloud Artist",
          songTitle: "SoundCloud Track",
          type: "Free",
          platform: "soundcloud",
          submissionTime: toEasternTime("2025-05-27T09:36:35.331Z"), // 4:36 AM ET
          url: "https://on.soundcloud.com/dvpjnwPokiHuvvtG9"
        },
        {
          id: "1748338677075",
          artistName: "YouTube Artist",
          songTitle: "YouTube Track",
          type: "VIP",
          platform: "youtube",
          submissionTime: toEasternTime("2025-05-27T09:37:57.075Z"), // 4:37 AM ET
          url: "https://music.youtube.com/watch?v=7vj1S9qqq7o"
        },
        {
          id: "1748338731002",
          artistName: "Spotify Artist",
          songTitle: "Spotify Track",
          type: "GA",
          platform: "spotify",
          submissionTime: toEasternTime("2025-05-27T09:38:51.002Z"), // 4:38 AM ET
          url: "https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185"
        }
      ]

      setSubmissions(realSubmissions)
      console.log(`📊 Loaded ${realSubmissions.length} real submissions with Eastern Time`)

    } catch (error) {
      console.error("Error fetching data from database:", error)
    }
  }

  const fetchAvailableTrackCards = async () => {
    try {
      setLoadingTrackCards(true)

      // Choose database based on availability
      const useLocal = shouldUseLocalDatabase()
      const database = useLocal ? localDatabase : supabase

      // Fetch from playlist table - this is the live queue/playlist for the player
      const { data, error } = await database
        .from("playlist")
        .select("*")
        .order("position", { ascending: true })

      if (error) throw error
      setAvailableTrackCards(data || [])

      // Select the first track card by default if none is selected
      if (data && data.length > 0) {
        if (!selectedTrackCardId1) {
          setSelectedTrackCardId1(data[0].id)
        }
        if (!selectedTrackCardId2) {
          setSelectedTrackCardId2(data[0].id)
        }
      }
    } catch (error) {
      console.error("Error fetching track cards:", error)
      toast({ title: "Error", description: "Failed to load track cards" })
    } finally {
      setLoadingTrackCards(false)
    }
  }

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const togglePlaylistLock = () => {
    setIsPlaylistLocked(!isPlaylistLocked)
  }

  const toggleLogo = () => {
    setShowLogo(!showLogo)
  }

  const autoGeneratePlaylist = () => {
    console.log("Auto generating playlist")
  }

  const toggleSubmissionExpand = (id: number) => {
    setExpandedItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  const addToPlaylist = (submission: QueueCardItem) => {
    // Add the submission to the queue
    const newPosition = queue.length + 1
    const newQueueItem = {
      ...submission,
      position: newPosition,
      id: Date.now() // Generate a unique ID
    }

    setQueue(prev => [...prev, newQueueItem])

    // Remove from submissions
    setSubmissions(prev => prev.filter(item => item.id !== submission.id))

    toast({
      title: "Added to Queue",
      description: `${submission.songTitle} by ${submission.artistName} added to queue`,
    })
  }

  const handleQueueReorder = (newItems: QueueCardItem[]) => {
    setQueue(newItems)

    // In a real app, you would also update the database
    // For example:
    // updatePlaylistOrder(newItems.map(item => ({ id: item.id, position: item.position })))

    toast({
      title: "Queue Updated",
      description: "The queue order has been updated",
      variant: "default",
    })
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
            <CheckCircle className="h-3 w-3 mr-1" /> Confirmed
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
            <Clock className="h-3 w-3 mr-1" /> Pending
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "VIP":
        return <Badge className="bg-gradient-to-r from-purple-600 to-indigo-600">VIP</Badge>
      case "Skip":
        return <Badge className="bg-gradient-to-r from-teal-500 to-emerald-500">Skip</Badge>
      case "GA":
        return <Badge className="bg-gradient-to-r from-amber-500 to-orange-500">GA</Badge>
      case "Free":
        return (
          <Badge variant="outline" className="border-gradient-primary">
            Free
          </Badge>
        )
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  // Placeholder for platform icon
  const getPlatformIcon = (platform: string) => {
    return <Music className="h-4 w-4" />
  }

  return (
    <div>
      <AdminsBigDock />
      <PageTransition>
        <div className="min-h-screen bg-gradient-to-b from-black via-purple-900/30 to-black relative overflow-hidden">
          <div className="on-air-light">ON AIR</div>
        <div className="container mx-auto px-4 pt-24 pb-12">
          {!isAuthenticated ? (
            <div className="max-w-md mx-auto bg-black/80 border border-purple-500/30 rounded-lg p-6 shadow-[0_0_15px_rgba(147,51,234,0.2)]">
              <h1 className="text-2xl font-bold text-white mb-8 text-center">Admin Login</h1>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-white">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter admin password"
                    className="bg-black/50 border-purple-500/30 text-white"
                    required
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white border-none"
                >
                  Login
                </Button>
                <p className="text-sm text-gray-400 text-center mt-4">
                  Use password: <span className="text-purple-400">admin123</span>
                </p>
              </form>
            </div>
          ) : (
            <>
              <h1 className="text-2xl font-bold text-white mb-8">Admin Dashboard</h1>

          {/* DJ Player Interface */}
          <div className="bg-black/90 border border-purple-500/30 rounded-lg p-6 shadow-[0_0_15px_rgba(147,51,234,0.2)] mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Music className="h-5 w-5 mr-2 text-purple-500" />
                STAYONBEAT
              </h2>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={fetchAvailableTrackCards}
                  disabled={loadingTrackCards}
                  className="bg-purple-900/20 border-purple-500/30"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`h-4 w-4 ${loadingTrackCards ? "animate-spin" : ""} text-purple-400`}>
                    <path d="M21 12a9 9 0 0 1-9 9m9-9a9 9 0 0 0-9-9m9 9H3m9 9a9 9 0 0 1-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9"></path>
                  </svg>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-purple-900/20 border-purple-500/30 text-purple-400"
                  onClick={() => {
                    // Just show a toast instead of navigating
                    toast({
                      title: "Feature Coming Soon",
                      description: "Playlist management will be available in the next update.",
                    })
                  }}
                >
                  Manage Playlists
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Preview Player */}
              <div className="bg-black/80 border border-purple-500/30 rounded-lg p-4 shadow-[0_0_15px_rgba(147,51,234,0.2)]">

                {selectedTrackCardId1 ? (
                  <SupabasePlaylistPlayer
                    playlistId={selectedTrackCardId1}
                    isAdmin={true}
                    showControls={true}
                    showLogo={showLogo}
                    className="w-full"
                  />
                ) : (
                  <div className="relative aspect-video rounded-md overflow-hidden">
                    {/* Slate color angle gradient for empty player */}
                    <div className="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-900"></div>

                    {/* Center play button */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Button
                        className="rounded-full w-16 h-16 bg-purple-600/20 border-2 border-purple-600/50 hover:bg-purple-600/30"
                        onClick={handlePlayPause}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                          <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                      </Button>
                    </div>

                    {/* Progress bar - standard position at bottom */}
                    <div className="absolute bottom-0 left-0 right-0 h-2 bg-black/50">
                      <div className="h-full bg-purple-500 w-0"></div>
                    </div>

                    {/* Time indicator */}
                    <div className="absolute bottom-3 right-2 text-xs text-white bg-black/50 px-2 py-1 rounded">
                      0:00 / 0:00
                    </div>
                  </div>
                )}
              </div>

              {/* Live Player */}
              <div className="bg-black/80 border border-purple-500/30 rounded-lg p-4 shadow-[0_0_15px_rgba(147,51,234,0.2)]">

                {selectedTrackCardId2 ? (
                  <div>
                    <SupabasePlaylistPlayer
                      playlistId={selectedTrackCardId2}
                      isAdmin={true}
                      showControls={false}
                      showLogo={showLogo}
                      className="w-full"
                    />

                    {/* Right player controls */}
                    <div className="flex justify-between items-center mt-3">
                      <div className="flex-1"></div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-purple-900/20 border-purple-500/30 text-purple-400"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                          </svg>
                          Play
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-purple-900/20 border-purple-500/30 text-purple-400"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <polygon points="5 4 15 12 5 20 5 4"></polygon>
                            <line x1="19" y1="5" x2="19" y2="19"></line>
                          </svg>
                          Next
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="relative aspect-video rounded-md overflow-hidden">
                      {/* Slate color angle gradient for empty player */}
                      <div className="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-900"></div>

                      {/* Center play button */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Button
                          className="rounded-full w-16 h-16 bg-purple-600/20 border-2 border-purple-600/50 hover:bg-purple-600/30"
                          onClick={handlePlayPause}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                          </svg>
                        </Button>
                      </div>

                      {/* Progress bar - standard position at bottom */}
                      <div className="absolute bottom-0 left-0 right-0 h-2 bg-black/50">
                        <div className="h-full bg-purple-500 w-0"></div>
                      </div>

                      {/* Time indicator */}
                      <div className="absolute bottom-3 right-2 text-xs text-white bg-black/50 px-2 py-1 rounded">
                        0:00 / 0:00
                      </div>
                    </div>

                    {/* Controls for empty player */}
                    <div className="flex justify-between items-center mt-3">
                      <div className="flex-1"></div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-purple-900/20 border-purple-500/30 text-purple-400"
                        >
                          Play
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-purple-900/20 border-purple-500/30 text-purple-400"
                        >
                          Next
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Player Controls Bottom */}
            <div className="flex justify-center mt-4">
              <div className="flex space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-purple-900/20 border-purple-500/30 text-purple-400"
                  onClick={toggleLogo}
                >
                  {showLogo ? "Hide Logo" : "Show Logo"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-purple-900/20 border-purple-500/30 text-purple-400"
                  onClick={togglePlaylistLock}
                >
                  {isPlaylistLocked ? "Unlock Playlist" : "Lock Playlist"}
                </Button>
              </div>
            </div>
          </div>

          {/* Three-column layout */}
          <SubmissionsDndContext
            submissions={submissions}
            queue={queue}
            onAddToQueue={addToPlaylist}
            onQueueReorder={handleQueueReorder}
            expandedItems={expandedItems}
            onExpand={toggleSubmissionExpand}
            isPlaylistLocked={isPlaylistLocked}
            neonTurquoiseStyle={neonTurquoiseStyle}
          >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Submissions */}
              <div className="bg-black/80 border border-purple-500/30 rounded-lg p-6 shadow-[0_0_15px_rgba(147,51,234,0.2)]">
                <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Music className="h-5 w-5 mr-2 text-purple-500" />
                  Submissions
                  <span className="ml-2 text-xs text-purple-400 font-normal bg-purple-900/20 px-2 py-1 rounded-full border border-purple-500/30">
                    Drag to Queue →
                  </span>
                </h2>
                <div className="flex justify-between items-center mb-4">
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => setExpandAll(!expandAll)}
                      className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white border-none"
                      size="sm"
                    >
                      {expandAll ? "Collapse All" : "Expand All"}
                    </Button>
                    <Button
                      onClick={() => {
                        console.log("🔄 Manual refresh button clicked")
                        fetchDataFromSupabase()
                      }}
                      className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white border-none"
                      size="sm"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                        <path d="M21 12a9 9 0 0 1-9 9m9-9a9 9 0 0 0-9-9m9 9H3m9 9a9 9 0 0 1-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s-1.343-9 3-9"></path>
                      </svg>
                      Refresh Data ({submissions.length} submissions)
                    </Button>
                  </div>
                  <Button
                    onClick={autoGeneratePlaylist}
                    className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white border-none"
                    size="sm"
                  >
                    Auto Generate
                  </Button>
                </div>
                <ScrollZone className="max-h-[400px] pr-2 custom-scrollbar">
                  {submissions.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-purple-400">No submissions yet</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {submissions.map((submission) => (
                        <DraggableSubmissionCard
                          key={submission.id}
                          item={submission}
                          neonTurquoiseStyle={neonTurquoiseStyle}
                        />
                      ))}
                    </div>
                  )}
                </ScrollZone>
              </div>

              {/* Now Playing */}
              <div className="bg-black/80 border border-purple-500/30 rounded-lg p-6 shadow-[0_0_15px_rgba(147,51,234,0.2)]">
                <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Music className="h-5 w-5 mr-2 text-purple-500" />
                  Now Playing
                </h2>
                <div className="bg-gradient-to-br from-purple-900/30 to-black p-4 rounded-lg border border-purple-500/20">
                  <div className="text-center py-8">
                    <p className="text-purple-400">No track currently playing</p>
                  </div>
                </div>

                {/* Queue Management */}
                <div className="mt-4">
                  <h3 className="text-lg font-semibold text-purple-400 mb-2 flex items-center">
                    Queue
                    <span className="ml-2 text-xs text-purple-400 font-normal bg-purple-900/20 px-2 py-1 rounded-full border border-purple-500/30">
                      Drop Zone
                    </span>
                  </h3>
                  <ScrollZone className="max-h-[200px] pr-2 custom-scrollbar">
                    <QueueDroppable
                      items={queue}
                      onReorder={handleQueueReorder}
                      expandedItems={expandedItems}
                      onExpand={toggleSubmissionExpand}
                      isUnlockMode={!isPlaylistLocked}
                      emptyMessage="Queue is empty"
                    />
                  </ScrollZone>
                </div>
              </div>

              {/* Played Tracks */}
              <div className="bg-black/80 border border-purple-500/30 rounded-lg p-6 shadow-[0_0_15px_rgba(147,51,234,0.2)]">
                <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Music className="h-5 w-5 mr-2 text-purple-500" />
                  Played Tracks
                </h2>
                <ScrollZone className="max-h-[400px] pr-2 custom-scrollbar">
                  {playedTracks.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-purple-400">No tracks played yet</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {playedTracks.map((track) => (
                        <div key={track.id} className="admin-card relative">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center">
                              <span className="font-mono text-sm w-6 text-gray-500">
                                {track.position?.toString().padStart(2, "0")}
                              </span>
                              <div className="ml-2">
                                <div className="flex items-center">
                                  <div className="mr-2 text-gray-400">
                                    {track.platform ? getPlatformIcon(track.platform) : <Music className="h-4 w-4" />}
                                  </div>
                                  <p className="font-medium line-clamp-1">{track.songTitle}</p>
                                </div>
                                <span className="text-sm" style={neonTurquoiseStyle}>
                                  {track.artistName}
                                </span>
                              </div>
                            </div>
                            <div className="flex flex-col items-end">
                              <Badge variant="default">{track.type}</Badge>
                              {track.submissionTime && (
                                <span className="text-xs text-gray-500 mt-1">{track.submissionTime}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollZone>
              </div>
            </div>
          </SubmissionsDndContext>


            </>
          )}
        </div>
      </div>
    </PageTransition>
    </div>
  )
}
