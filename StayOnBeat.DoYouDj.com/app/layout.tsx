import type React from "react";
import "@/app/globals.css";
import { ThemeProvider } from "@/components/theme-provider";
// import { BackgroundProvider } from "@/components/background-provider"; // Assuming this is not strictly needed for the fix, can be re-added if necessary
import type { Metadata } from "next";
import { orbitron, tiltNeon, neonderthaw, neonify } from "./fonts";
import { NextSSRPlugin } from "@uploadthing/react/next-ssr-plugin";
import { extractRouterConfig } from "uploadthing/server";
import { ourFileRouter } from "@/app/api/uploadthing/core";
import { ClientLayoutWrapper } from "@/components/ClientLayoutWrapper";
import { SupabaseProvider } from "@/lib/supabase";
import { ClerkProvider } from '@clerk/nextjs';

export const metadata: Metadata = {
  title: "StayOnBeat",
  description: "Submissions Queues",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body className={`${orbitron.variable} ${tiltNeon.variable} ${neonderthaw.variable} ${neonify.variable} flex flex-col min-h-screen`}>
          <NextSSRPlugin
            /**
             * The `extractRouterConfig` will extract **only** the route configs
             * from the router to prevent additional information from being
             * leaked to the client.
             */
            routerConfig={extractRouterConfig(ourFileRouter)}
          />
          <SupabaseProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <ClientLayoutWrapper>
                {children}
              </ClientLayoutWrapper>
            </ThemeProvider>
          </SupabaseProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
